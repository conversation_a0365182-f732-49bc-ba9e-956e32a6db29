<!-- TOC -->

- [大模型服务器相关接口说明](#大模型服务器相关接口说明)
 - [三合一接口](#三合一接口)
 - [语种定义](#语种定义)
 - [错误定义](#错误定义)

<!-- /TOC -->

# 大模型服务器相关接口说明
    使用json格式通信，websocket（ws://yf.jfgou.com:548/llm）方式传输
    reqId 格式 前缀 dev_8位中英文数字随机数_自增id, dev_abcd1234_1。8位中英文数字随机数每次开机时生成即可

## 三合一接口

### 请求参数
| 字段名	| 类型	| 是否必传	| 说明																		|
| :---------| :-----| :------	| :---------------------------------------------------------				|
| opcode	| string| 是		| asr:语音转文字 asr2trans:翻译 asr2trans2tts:翻译并合成语音				|
| reqId		| string| 否		| 请求id，原样返回。以第一音频帧的为主，后续的帧可以不发					|
| token		| string| 否		| 通过业务服务器获取，发送第一音频帧必发，后续的帧可以不发					|
| sessionId	| string| 否		| 会话id，用于标记整个会话的数据，发送第一音频帧必发，后续的帧可以不发		|
| status	| int	| 是		| 音频状态 0 :第一帧音频 1 :中间的音频 2 :最后一帧音频，最后一帧必须要发送	|
| format	| int	| 否		| 0-默认16位 1：8位pcm 2：16位pcm。发送第一音频帧必发，后续的帧可以不发		|
| outFormat	| int	| 否		| 0-默认16位 1：8位pcm 2：16位pcm。发送第一音频帧必发，后续的帧可以不发		|
| audio		| string| 是		| base64音频数据															|
| lang		| string| 否		| 本端语言。发送第一音频帧必发，后续的帧可以不发							|
| transLang	| string| 否		| 翻译的语言。发送第一音频帧必发，后续的帧可以不发							|
| front		| bool	| 否		| true-A(正)面 false-B面。发送第一音频帧必发，后续的帧可以不发				|
| sn		| string| 否		| 设备SN，用于记录翻译数据。发送第一音频帧必发，后续的帧可以不发			|
| ttsFlag	| int	| 否		| tts返回方式，0-base64，1-[]byte原始数据									|

### 返回参数
| 字段名	| 类型		| 说明																|
| :--------	| :--------	| :--------------------------------									|
| opcode	| string	| asr:语音转文字 asr2trans:翻译 asr2trans2tts:翻译并合成语音		|
| reqId		| string	| 请求id，原样返回													|
| code		| int		| 返回码															|
| msg		| string	| 返回消息															|
| status	| int		| 音频状态 0 :第一帧音频 1 :中间的音频 2 :最后一帧音频				|
| lang		| string	| 本端语言															|
| transLang	| string	| 翻译的语言														|
| outFormat	| int		| 0-默认16位 1：8位pcm 2：16位pcm。									|
| asr		| string	| 语音转文本														|
| trans		| string	| 翻译的文本。opcode=asr2trans/asr2trans2tts时才会返回				|
| tts		| string	| 合成的base64音频数据。opcode=asr2trans2tts[ttsFlag=0]时才会返回	|
| ttsByte	| []byte	| 合成的原始音频数据。opcode=asr2trans2tts[ttsFlag=1]时才会返回		|

```
以asr2trans为例子说明
首帧发送
{
	"opcode": "asr2trans",
	"reqId": "xxx",
	"token": "xxx",
	"status": 0,
	"format": 2,
	"outFormat": 2,
	"audio": "base64音频数据",
	"lang": "zh",
	"transLang": "",
	"front": true,
	"sn": "xxx"
}

中间帧发送
{
	"opcode": "asr2trans",
	"status": 1,
	"audio": "base64音频数据"
}

结束帧发送
{
	"opcode": "asr2trans",
	"status": 2,
	"audio": "base64音频数据"
}

```


## 语种定义

```
"zh": "中文"
"en": "英文"
"ja": "日文"
"ko": "韩文"
"de": "德语"
"ru": "俄文"
"fr": "法语"
"it": "意大利语"
"th": "泰语"
"fil": "菲律宾语"
"es": "西班牙语"
"vi": "越南语"
"id": "印尼语"
"pt": "葡萄牙语" --阿里云不支持
"ar": "阿拉伯语" --阿里云不支持
```


## 错误定义

<pre>
//错误定义
const (
	ELLMOK             int32 = iota
	ELLMDB                   //数据库
	ELLMUnknown              //未知错误
	ELLMSessionTimeout       //会话超时
	ELLMInvalidParam         //参数错误
	ELLMOpcodeNotExist       //方法不存在
	ELLMSystemBusy           //系统繁忙
)

//账号、能力相关错误信息定义
const (
	ELLMToken     int32 = iota + 50 //token错误
	ELLMConnLimit                   //并发能力限制
)
<pre>